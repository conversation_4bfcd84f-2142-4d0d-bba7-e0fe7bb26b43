#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""اختبار سريع"""

import sqlite3

try:
    print("🔍 اختبار سريع...")
    
    conn = sqlite3.connect('data.db')
    cursor = conn.cursor()
    
    # فحص هيكل الجدول
    cursor.execute("PRAGMA table_info(registration_fees)")
    columns = cursor.fetchall()
    
    print(f"📋 أعمدة جدول registration_fees:")
    for col in columns:
        print(f"   - {col[1]}")
    
    conn.close()
    print("✅ الاختبار مكتمل!")
    
except Exception as e:
    print(f"❌ خطأ: {str(e)}")
