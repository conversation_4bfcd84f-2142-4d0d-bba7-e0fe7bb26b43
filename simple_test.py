#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os

# تحقق من مجلد التقارير
reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير واجبات التسجيل')
print(f"مجلد التقارير: {reports_dir}")

if os.path.exists(reports_dir):
    print("✅ المجلد موجود")
    files = os.listdir(reports_dir)
    print(f"عدد الملفات: {len(files)}")
    for file in files:
        print(f"  - {file}")
else:
    print("❌ المجلد غير موجود")
    print("سأنشئ المجلد...")
    os.makedirs(reports_dir, exist_ok=True)
    print("✅ تم إنشاء المجلد")

# تحقق من سطح المكتب
desktop = os.path.join(os.path.expanduser('~'), 'Desktop')
print(f"\nسطح المكتب: {desktop}")
desktop_items = os.listdir(desktop)
print("محتويات سطح المكتب:")
for item in desktop_items:
    if 'تقارير' in item:
        print(f"  📁 {item}")
