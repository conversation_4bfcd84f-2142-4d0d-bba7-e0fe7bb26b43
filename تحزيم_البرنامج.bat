@echo off
chcp 65001 > nul
title تحزيم برنامج المعين في الحراسة العامة

echo ===============================================================================
echo                    🚀 تحزيم برنامج المعين في الحراسة العامة
echo ===============================================================================
echo.
echo 📋 هذا الملف سيقوم بتحزيم البرنامج ليعمل على أي جهاز
echo ⏳ العملية قد تستغرق 10-15 دقيقة حسب سرعة الجهاز
echo.

:: التحقق من وجود Python
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت على النظام
    echo 📥 يرجى تثبيت Python 3.7 أو أحدث من: https://python.org
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
echo.

:: التحقق من وجود ملف التحزيم
if not exist "build_executable.py" (
    echo ❌ خطأ: ملف التحزيم غير موجود
    echo 📁 تأكد من وجود ملف "build_executable.py" في نفس المجلد
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على ملف التحزيم
echo.

:: التحقق من وجود الملفات الأساسية
echo 🔍 فحص الملفات الأساسية...
set missing_files=0

if not exist "main_window.py" (
    echo ❌ ملف main_window.py مفقود
    set missing_files=1
)

if not exist "data.db" (
    echo ❌ ملف data.db مفقود
    set missing_files=1
)

if not exist "01.ico" (
    echo ❌ ملف 01.ico مفقود
    set missing_files=1
)

if %missing_files%==1 (
    echo.
    echo ❌ بعض الملفات الأساسية مفقودة
    echo 📁 تأكد من وجود جميع ملفات البرنامج في نفس المجلد
    echo.
    pause
    exit /b 1
)

echo ✅ جميع الملفات الأساسية موجودة
echo.

:: بدء عملية التحزيم
echo 🚀 بدء عملية التحزيم...
echo ⚠️ لا تغلق هذه النافذة حتى انتهاء العملية
echo.

python build_executable.py

:: التحقق من نجاح العملية
if errorlevel 1 (
    echo.
    echo ❌ فشلت عملية التحزيم
    echo 📝 راجع الرسائل أعلاه لمعرفة السبب
) else (
    echo.
    echo 🎉 تم إكمال التحزيم بنجاح!
    echo 📦 ستجد البرنامج المحزم في مجلد "البرنامج_المحزم"
    echo 📁 والأرشيف المضغوط في نفس المجلد
)

echo.
echo 📝 اضغط أي مفتاح للخروج...
pause > nul
