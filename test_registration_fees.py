#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""اختبار جدول واجبات التسجيل"""

import sqlite3

def test_registration_fees_table():
    """اختبار جدول واجبات التسجيل"""
    try:
        print("🔍 اختبار جدول واجبات التسجيل...")
        
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # فحص هيكل الجدول
        cursor.execute("PRAGMA table_info(registration_fees)")
        columns = cursor.fetchall()
        
        print(f"📋 أعمدة جدول registration_fees ({len(columns)}):")
        for col in columns:
            print(f"   - {col[1]} ({col[2]})")
        
        # فحص عدد السجلات
        cursor.execute("SELECT COUNT(*) FROM registration_fees")
        count = cursor.fetchone()[0]
        print(f"📊 عدد السجلات: {count}")
        
        # اختبار استعلام التحديث
        print("\n🔧 اختبار استعلام التحديث...")
        cursor.execute("""
            UPDATE registration_fees
            SET 
                القسم = COALESCE(
                    (SELECT jb.القسم FROM جدول_البيانات jb WHERE jb.id = registration_fees.student_id),
                    registration_fees.القسم
                ),
                اسم_الاستاذ = (
                    SELECT mat.اسم_الاستاذ 
                    FROM جدول_المواد_والاقسام mat
                    JOIN جدول_البيانات jb ON mat.القسم = jb.القسم
                    WHERE jb.id = registration_fees.student_id
                    LIMIT 1
                ),
                المادة = (
                    SELECT mat.المادة 
                    FROM جدول_المواد_والاقسام mat
                    JOIN جدول_البيانات jb ON mat.القسم = jb.القسم
                    WHERE jb.id = registration_fees.student_id
                    LIMIT 1
                ),
                النوع = (
                    SELECT jb.النوع 
                    FROM جدول_البيانات jb
                    WHERE jb.id = registration_fees.student_id
                )
            WHERE registration_fees.القسم IS NULL OR registration_fees.القسم = '' 
               OR registration_fees.اسم_الاستاذ IS NULL OR registration_fees.المادة IS NULL 
               OR registration_fees.النوع IS NULL
        """)
        
        updated_rows = cursor.rowcount
        print(f"✅ تم تحديث {updated_rows} سجل بنجاح")
        
        conn.commit()
        conn.close()
        
        print("✅ اختبار جدول واجبات التسجيل مكتمل بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_registration_fees_table()
