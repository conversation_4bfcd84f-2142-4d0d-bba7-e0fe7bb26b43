@echo off
chcp 65001 > nul
title فحص جاهزية البرنامج للتحزيم

echo ===============================================================================
echo                    🔍 فحص جاهزية البرنامج للتحزيم
echo ===============================================================================
echo.
echo 📋 هذا الملف سيفحص جاهزية البرنامج قبل التحزيم
echo ⚡ الفحص سريع ولن يستغرق أكثر من دقيقة
echo.

:: التحقق من وجود Python
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت على النظام
    echo 📥 يرجى تثبيت Python 3.7 أو أحدث من: https://python.org
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
echo.

:: التحقق من وجود ملف الفحص
if not exist "فحص_جاهزية_التحزيم.py" (
    echo ❌ خطأ: ملف الفحص غير موجود
    echo 📁 تأكد من وجود ملف "فحص_جاهزية_التحزيم.py" في نفس المجلد
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على ملف الفحص
echo.

:: بدء عملية الفحص
echo 🚀 بدء فحص الجاهزية...
echo.

python "فحص_جاهزية_التحزيم.py"

echo.
echo 📝 انتهى الفحص
pause
