#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
===============================================================================
                    فحص جاهزية البرنامج للتحزيم
===============================================================================
ملف للتحقق من جاهزية جميع ملفات البرنامج قبل عملية التحزيم
===============================================================================
"""

import os
import sys
import sqlite3
import importlib.util
from datetime import datetime

class ReadinessChecker:
    """فئة فحص جاهزية التحزيم"""
    
    def __init__(self):
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        self.errors = []
        self.warnings = []
        self.success_count = 0
        self.total_checks = 0
        
        # قائمة الملفات الأساسية
        self.essential_files = [
            "main_window.py",
            "sub01_window.py", 
            "sub2_window.py",
            "sub8_window.py",
            "sub252_window.py",
            "sub262_window.py",
            "sub232_window.py",
            "sub3_window.py",
            "sub100_window.py",
            "attendance_processing_window.py",
            "budget_planning_window.py",
            "expense_management_window.py",
            "cash_flow_window.py",
            "financial_system_launcher.py",
            "monthly_duties_window.py",
            "archived_accounts_manager.py",
            "check_database.py",
            "01.ico",
            "data.db"
        ]
        
        # ملفات الطباعة
        self.print_files = [
            "print101.py",
            "print111.py",
            "print144.py", 
            "print_registration_fees.py",
            "print_registration_fees_monthly_style.py",
            "print_registration_fees_simple.py",
            "print_section_monthly.py",
            "print_section_yearly.py",
            "attendance_sheet_report.py",
            "daily_attendance_sheet_report.py"
        ]
        
        # المكتبات المطلوبة
        self.required_modules = [
            "PyQt5",
            "sqlite3",
            "fpdf",
            "arabic_reshaper", 
            "bidi",
            "openpyxl",
            "matplotlib",
            "numpy",
            "PIL"
        ]

    def print_header(self):
        """طباعة رأس الفحص"""
        print("=" * 80)
        print("                    🔍 فحص جاهزية البرنامج للتحزيم")
        print("=" * 80)
        print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📁 مجلد البرنامج: {self.script_dir}")
        print("=" * 80)

    def check_python_version(self):
        """فحص إصدار Python"""
        print("\n🐍 فحص إصدار Python...")
        self.total_checks += 1
        
        version = sys.version_info
        print(f"   إصدار Python: {version.major}.{version.minor}.{version.micro}")
        
        if version.major < 3:
            self.errors.append("Python 2 غير مدعوم - يتطلب Python 3.7+")
            print("   ❌ Python 2 غير مدعوم")
            return False
        elif version.major == 3 and version.minor < 7:
            self.errors.append(f"Python {version.major}.{version.minor} قديم - يتطلب Python 3.7+")
            print(f"   ❌ Python {version.major}.{version.minor} قديم")
            return False
        else:
            print("   ✅ إصدار Python مناسب")
            self.success_count += 1
            return True

    def check_essential_files(self):
        """فحص الملفات الأساسية"""
        print("\n📋 فحص الملفات الأساسية...")
        
        missing_files = []
        
        for file in self.essential_files:
            self.total_checks += 1
            file_path = os.path.join(self.script_dir, file)
            
            if os.path.exists(file_path):
                print(f"   ✅ {file}")
                self.success_count += 1
                
                # فحص إضافي لملفات Python
                if file.endswith('.py'):
                    if self.check_python_syntax(file_path):
                        print(f"      ✅ صيغة صحيحة")
                    else:
                        self.warnings.append(f"مشكلة في صيغة {file}")
                        print(f"      ⚠️ مشكلة في الصيغة")
                        
            else:
                print(f"   ❌ {file} - مفقود")
                missing_files.append(file)
                self.errors.append(f"ملف مفقود: {file}")
        
        return len(missing_files) == 0

    def check_print_files(self):
        """فحص ملفات الطباعة"""
        print("\n🖨️ فحص ملفات الطباعة...")
        
        missing_print_files = []
        
        for file in self.print_files:
            self.total_checks += 1
            file_path = os.path.join(self.script_dir, file)
            
            if os.path.exists(file_path):
                print(f"   ✅ {file}")
                self.success_count += 1
            else:
                print(f"   ⚠️ {file} - مفقود (اختياري)")
                missing_print_files.append(file)
                self.warnings.append(f"ملف طباعة مفقود: {file}")
        
        if missing_print_files:
            print(f"\n   📝 ملاحظة: {len(missing_print_files)} ملف طباعة مفقود")
            print("   💡 ملفات الطباعة اختيارية - البرنامج سيعمل بدونها")
        
        return True

    def check_database(self):
        """فحص قاعدة البيانات"""
        print("\n🗄️ فحص قاعدة البيانات...")
        self.total_checks += 1
        
        db_path = os.path.join(self.script_dir, "data.db")
        
        if not os.path.exists(db_path):
            self.errors.append("ملف قاعدة البيانات data.db مفقود")
            print("   ❌ ملف قاعدة البيانات مفقود")
            return False
        
        try:
            # فحص إمكانية الاتصال بقاعدة البيانات
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # فحص وجود الجداول الأساسية
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            essential_tables = [
                'بيانات_المؤسسة',
                'جدول_البيانات',
                'جدول_المواد_والاقسام'
            ]
            
            missing_tables = []
            for table in essential_tables:
                if table in tables:
                    print(f"   ✅ جدول {table}")
                else:
                    missing_tables.append(table)
                    print(f"   ⚠️ جدول {table} مفقود")
            
            if missing_tables:
                self.warnings.append(f"جداول مفقودة: {', '.join(missing_tables)}")
            
            # فحص حجم قاعدة البيانات
            db_size = os.path.getsize(db_path) / 1024  # KB
            print(f"   📊 حجم قاعدة البيانات: {db_size:.2f} KB")
            
            if db_size < 10:
                self.warnings.append("قاعدة البيانات صغيرة جداً - قد تكون فارغة")
                print("   ⚠️ قاعدة البيانات صغيرة جداً")
            
            conn.close()
            print("   ✅ قاعدة البيانات سليمة")
            self.success_count += 1
            return True
            
        except Exception as e:
            self.errors.append(f"خطأ في قاعدة البيانات: {str(e)}")
            print(f"   ❌ خطأ في قاعدة البيانات: {str(e)}")
            return False

    def check_required_modules(self):
        """فحص المكتبات المطلوبة"""
        print("\n📦 فحص المكتبات المطلوبة...")
        
        missing_modules = []
        
        for module in self.required_modules:
            self.total_checks += 1
            
            try:
                if module == "sqlite3":
                    import sqlite3
                elif module == "PyQt5":
                    import PyQt5
                elif module == "fpdf":
                    import fpdf
                elif module == "arabic_reshaper":
                    import arabic_reshaper
                elif module == "bidi":
                    import bidi
                elif module == "openpyxl":
                    import openpyxl
                elif module == "matplotlib":
                    import matplotlib
                elif module == "numpy":
                    import numpy
                elif module == "PIL":
                    import PIL
                
                print(f"   ✅ {module}")
                self.success_count += 1
                
            except ImportError:
                print(f"   ❌ {module} - غير مثبت")
                missing_modules.append(module)
                self.errors.append(f"مكتبة مفقودة: {module}")
        
        if missing_modules:
            print(f"\n   📝 لتثبيت المكتبات المفقودة:")
            print(f"   pip install {' '.join(missing_modules)}")
        
        return len(missing_modules) == 0

    def check_folders(self):
        """فحص المجلدات المطلوبة"""
        print("\n📁 فحص المجلدات...")
        
        required_folders = ["fonts", "logs", "reports"]
        
        for folder in required_folders:
            self.total_checks += 1
            folder_path = os.path.join(self.script_dir, folder)
            
            if os.path.exists(folder_path):
                print(f"   ✅ {folder}/")
                self.success_count += 1
            else:
                print(f"   ⚠️ {folder}/ - مفقود (سيتم إنشاؤه)")
                self.warnings.append(f"مجلد مفقود: {folder}")
                
                # إنشاء المجلد
                try:
                    os.makedirs(folder_path, exist_ok=True)
                    print(f"   ✅ تم إنشاء {folder}/")
                    self.success_count += 1
                except Exception as e:
                    self.errors.append(f"فشل في إنشاء مجلد {folder}: {str(e)}")
                    print(f"   ❌ فشل في إنشاء {folder}/")
        
        return True

    def check_python_syntax(self, file_path):
        """فحص صيغة ملف Python"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                source = f.read()
            compile(source, file_path, 'exec')
            return True
        except SyntaxError:
            return False
        except Exception:
            return True  # قد يكون خطأ في الترميز وليس الصيغة

    def check_icon_file(self):
        """فحص ملف الأيقونة"""
        print("\n🎨 فحص ملف الأيقونة...")
        self.total_checks += 1
        
        icon_path = os.path.join(self.script_dir, "01.ico")
        
        if os.path.exists(icon_path):
            icon_size = os.path.getsize(icon_path)
            print(f"   ✅ 01.ico موجود ({icon_size} بايت)")
            
            if icon_size < 1000:
                self.warnings.append("ملف الأيقونة صغير جداً")
                print("   ⚠️ الأيقونة صغيرة جداً")
            
            self.success_count += 1
            return True
        else:
            self.errors.append("ملف الأيقونة 01.ico مفقود")
            print("   ❌ ملف الأيقونة مفقود")
            return False

    def print_summary(self):
        """طباعة ملخص الفحص"""
        print("\n" + "=" * 80)
        print("                        📊 ملخص فحص الجاهزية")
        print("=" * 80)
        
        success_rate = (self.success_count / self.total_checks * 100) if self.total_checks > 0 else 0
        
        print(f"📈 معدل النجاح: {success_rate:.1f}% ({self.success_count}/{self.total_checks})")
        print(f"✅ فحوصات ناجحة: {self.success_count}")
        print(f"❌ أخطاء: {len(self.errors)}")
        print(f"⚠️ تحذيرات: {len(self.warnings)}")
        
        if self.errors:
            print(f"\n❌ الأخطاء ({len(self.errors)}):")
            for i, error in enumerate(self.errors, 1):
                print(f"   {i}. {error}")
        
        if self.warnings:
            print(f"\n⚠️ التحذيرات ({len(self.warnings)}):")
            for i, warning in enumerate(self.warnings, 1):
                print(f"   {i}. {warning}")
        
        print("\n" + "=" * 80)
        
        if len(self.errors) == 0:
            print("🎉 البرنامج جاهز للتحزيم!")
            print("💡 يمكنك الآن تشغيل ملف 'تحزيم_البرنامج.bat'")
        else:
            print("❌ البرنامج غير جاهز للتحزيم")
            print("🔧 يرجى إصلاح الأخطاء أولاً")
        
        print("=" * 80)

    def run_check(self):
        """تشغيل فحص الجاهزية الكامل"""
        self.print_header()
        
        # تشغيل جميع الفحوصات
        checks = [
            self.check_python_version,
            self.check_essential_files,
            self.check_print_files,
            self.check_database,
            self.check_required_modules,
            self.check_folders,
            self.check_icon_file
        ]
        
        for check in checks:
            try:
                check()
            except Exception as e:
                self.errors.append(f"خطأ في الفحص: {str(e)}")
                print(f"   ❌ خطأ غير متوقع: {str(e)}")
        
        # طباعة الملخص
        self.print_summary()
        
        return len(self.errors) == 0

def main():
    """الدالة الرئيسية"""
    checker = ReadinessChecker()
    
    try:
        is_ready = checker.run_check()
        
        if is_ready:
            print("\n🚀 هل تريد بدء عملية التحزيم الآن؟ (y/n): ", end="")
            response = input().lower().strip()
            
            if response in ['y', 'yes', 'نعم', 'ن']:
                print("\n🔄 بدء عملية التحزيم...")
                import subprocess
                subprocess.run([sys.executable, "build_executable.py"])
            else:
                print("\n📝 يمكنك تشغيل التحزيم لاحقاً باستخدام 'تحزيم_البرنامج.bat'")
        
        input("\n📝 اضغط Enter للخروج...")
        
    except KeyboardInterrupt:
        print("\n\n⏹️ تم إيقاف الفحص بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {str(e)}")
        input("\n📝 اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
