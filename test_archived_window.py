#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""اختبار نافذة الحسابات المرحلة"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from archived_accounts_window import ArchivedAccountsWindow

def main():
    """تشغيل نافذة الحسابات المرحلة للاختبار"""
    try:
        app = QApplication(sys.argv)
        
        # إنشاء النافذة
        window = ArchivedAccountsWindow()
        window.show()
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"خطأ في تشغيل النافذة: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
