===============================================================================
                    تعليمات تحزيم برنامج المعين في الحراسة العامة
===============================================================================

🎯 الهدف:
تحزيم البرنامج ليعمل على أي جهاز كمبيوتر بدون الحاجة لتثبيت Python أو المكتبات

📋 المتطلبات قبل التحزيم:
• Python 3.7 أو أحدث مثبت على النظام
• جميع ملفات البرنامج في نفس المجلد
• اتصال بالإنترنت لتحميل المكتبات

🚀 طريقة التحزيم السريعة:
1. تأكد من وجود جميع ملفات البرنامج في نفس المجلد
2. شغل ملف "تحزيم_البرنامج.bat"
3. انتظر حتى انتهاء العملية (10-15 دقيقة)
4. ستجد البرنامج المحزم في مجلد "البرنامج_المحزم"

🔧 طريقة التحزيم اليدوية:
1. افتح موجه الأوامر (Command Prompt)
2. انتقل لمجلد البرنامج: cd "مسار_المجلد"
3. ثبت المكتبات: pip install -r requirements.txt
4. شغل ملف التحزيم: python build_executable.py

📁 الملفات المطلوبة للتحزيم:
✅ الملفات الأساسية:
• main_window.py - النافذة الرئيسية
• sub01_window.py - نافذة بيانات المؤسسة
• sub2_window.py - نافذة إدارة البيانات
• sub8_window.py - نافذة الإعدادات
• sub252_window.py - نافذة اللوائح والأقسام
• sub262_window.py - نافذة الحضور والغياب
• sub232_window.py - نافذة الواجبات الشهرية
• sub3_window.py - نافذة رسوم التسجيل
• sub100_window.py - نافذة الحوارات المخصصة
• attendance_processing_window.py - معالجة الحضور
• budget_planning_window.py - تخطيط الموازنة
• expense_management_window.py - إدارة المصاريف
• cash_flow_window.py - التدفقات النقدية
• financial_system_launcher.py - مشغل النظام المالي
• monthly_duties_window.py - الواجبات الشهرية
• archived_accounts_manager.py - إدارة الحسابات المؤرشفة
• check_database.py - فحص قاعدة البيانات
• 01.ico - أيقونة البرنامج
• data.db - قاعدة البيانات

✅ ملفات الطباعة (اختيارية):
• print101.py - طباعة التقارير الأساسية
• print111.py - طباعة تقارير الحضور
• print144.py - طباعة تقارير الواجبات
• print_registration_fees.py - طباعة رسوم التسجيل
• print_registration_fees_monthly_style.py - طباعة رسوم شهرية
• print_registration_fees_simple.py - طباعة رسوم بسيطة
• print_section_monthly.py - طباعة تقارير الأقسام الشهرية
• print_section_yearly.py - طباعة تقارير الأقسام السنوية
• attendance_sheet_report.py - تقرير كشف الحضور
• daily_attendance_sheet_report.py - تقرير الحضور اليومي

✅ المجلدات (ستُنشأ تلقائياً إذا لم تكن موجودة):
• fonts/ - مجلد الخطوط
• logs/ - مجلد السجلات
• reports/ - مجلد التقارير

🔍 فحص ما بعد التحزيم:
• تأكد من وجود مجلد "البرنامج_المحزم"
• تأكد من وجود ملف "المعين_في_الحراسة_العامة.exe"
• جرب تشغيل البرنامج للتأكد من عمله
• تأكد من وجود جميع الملفات المرفقة

📦 محتويات الحزمة النهائية:
• البرنامج/ - مجلد البرنامج الرئيسي
  ├── المعين_في_الحراسة_العامة.exe - الملف التنفيذي
  ├── data.db - قاعدة البيانات
  ├── 01.ico - أيقونة البرنامج
  ├── fonts/ - مجلد الخطوط
  ├── logs/ - مجلد السجلات
  ├── reports/ - مجلد التقارير
  └── [ملفات النظام الأخرى]
• تشغيل_البرنامج.bat - ملف التشغيل السريع
• اقرأني.txt - تعليمات الاستخدام
• متطلبات_النظام.txt - متطلبات التشغيل
• معلومات_الإصدار.json - تفاصيل الإصدار

🎯 اختبار البرنامج المحزم:
1. انسخ مجلد "البرنامج_المحزم" لجهاز آخر
2. شغل ملف "تشغيل_البرنامج.bat"
3. تأكد من عمل جميع الوظائف
4. جرب إنشاء تقرير PDF
5. تأكد من حفظ البيانات

⚠️ ملاحظات مهمة:
• العملية قد تستغرق 10-15 دقيقة
• تأكد من وجود مساحة كافية على القرص (2-3 GB)
• لا تغلق النافذة أثناء التحزيم
• في حالة الفشل، تأكد من تثبيت جميع المكتبات
• قد تحتاج لتعطيل مكافح الفيروسات مؤقتاً

🛠️ استكشاف الأخطاء:
• إذا فشل التحزيم: تأكد من تثبيت PyInstaller
• إذا كان البرنامج بطيئاً: استخدم SSD بدلاً من HDD
• إذا لم تعمل الطباعة: تأكد من وجود ملفات الطباعة
• إذا ظهرت أخطاء: راجع ملف السجل

📞 الدعم الفني:
في حالة مواجهة مشاكل، تأكد من:
• تحديث Python لآخر إصدار
• تثبيت جميع المكتبات من requirements.txt
• وجود جميع ملفات البرنامج
• صلاحيات الكتابة في المجلد

===============================================================================
تم إعداد هذه التعليمات لضمان نجاح عملية التحزيم
© 2025 - فريق تطوير أنظمة إدارة المؤسسات التعليمية
===============================================================================
