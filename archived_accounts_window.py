#!/usr/bin/env python3
# -*- coding: utf-8 -*-
#    """نافذة إدارة الحسابات المرحلة"""

import sys
import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from datetime import datetime
import sqlite3

class ArchivedAccountsManager:
    """مدير الحسابات المرحلة"""

    def __init__(self, db_path="data.db"):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """إنشاء جداول الحسابات المرحلة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إنشاء جدول الحسابات المرحلة
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS الحسابات_المرحلة (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    student_id INTEGER,
                    month TEXT NOT NULL,
                    year INTEGER NOT NULL,
                    amount_required REAL DEFAULT 0,
                    amount_paid REAL DEFAULT 0,
                    payment_status TEXT DEFAULT 'غير مدفوع',
                    ترحيل_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(student_id, month, year)
                )
            """)

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"خطأ في إنشاء قاعدة البيانات: {str(e)}")

    def archive_monthly_accounts(self, month, year, overwrite=False):
        """ترحيل حسابات شهر معين"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # التحقق من وجود بيانات مرحلة مسبقاً
            if not overwrite:
                cursor.execute("""
                    SELECT COUNT(*) FROM الحسابات_المرحلة
                    WHERE month = ? AND year = ?
                """, (month, year))

                if cursor.fetchone()[0] > 0:
                    conn.close()
                    return False, f"الشهر {month}/{year} مرحل مسبقاً"

            # حذف البيانات المرحلة السابقة إذا كان الإعادة مطلوبة
            if overwrite:
                cursor.execute("""
                    DELETE FROM الحسابات_المرحلة
                    WHERE month = ? AND year = ?
                """, (month, year))

            # ترحيل البيانات من جدول monthly_duties
            cursor.execute("""
                INSERT OR REPLACE INTO الحسابات_المرحلة
                (student_id, month, year, amount_required, amount_paid, payment_status)
                SELECT student_id, month, year, amount_required, amount_paid, payment_status
                FROM monthly_duties
                WHERE month = ? AND year = ?
            """, (month, year))

            archived_count = cursor.rowcount
            conn.commit()
            conn.close()

            return True, f"تم ترحيل {archived_count} سجل للشهر {month}/{year}"

        except Exception as e:
            return False, f"خطأ في الترحيل: {str(e)}"

    def delete_archived_month(self, month, year):
        """حذف شهر مرحل"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                DELETE FROM الحسابات_المرحلة
                WHERE month = ? AND year = ?
            """, (month, year))

            deleted_count = cursor.rowcount
            conn.commit()
            conn.close()

            return True, f"تم حذف {deleted_count} سجل للشهر {month}/{year}"

        except Exception as e:
            return False, f"خطأ في الحذف: {str(e)}"

class ArchivedAccountsWindow(QMainWindow):
    """نافذة إدارة الحسابات المرحلة"""
    
    def __init__(self):
        super().__init__()
        self.manager = ArchivedAccountsManager()
        self.init_ui()
        self.load_archived_months()
    
    def center_window(self, window):
        """توسيط النافذة في وسط الشاشة"""
        try:
            # الحصول على أبعاد الشاشة
            screen = QApplication.desktop().screenGeometry()
            # الحصول على أبعاد النافذة
            window_size = window.geometry()
            # حساب الموقع المركزي
            x = (screen.width() - window_size.width()) // 2
            y = (screen.height() - window_size.height()) // 2
            # تطبيق الموقع الجديد
            window.move(x, y)
        except Exception as e:
            print(f"خطأ في توسيط النافذة: {str(e)}")

    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🏦 إدارة الحسابات المرحلة")
        self.setGeometry(100, 100, 1200, 800)

        # توسيط النافذة الرئيسية
        self.center_window(self)

        # الويدجيت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)

        # العنوان الرئيسي
        title_label = QLabel("🏦 إدارة الحسابات المرحلة")
        title_label.setFont(QFont("Calibri", 15, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #2c3e50;
                color: white;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)

        # مجموعة البحث والتصفية
        self.create_search_group(main_layout)

        self.create_buttons_group(main_layout)

        # جدول الحسابات المرحلة
        self.create_archived_table(main_layout)

        # شريط الحالة
        self.statusBar().setFont(QFont("Calibri", 13, QFont.Bold))
        self.statusBar().showMessage("جاهز - نظام الحسابات المرحلة")

    def create_search_group(self, main_layout):
        """إنشاء مجموعة البحث والتصفية"""
        search_group = QGroupBox("🔍 البحث والتصفية")
        search_group.setFont(QFont("Calibri", 14, QFont.Bold))
        search_layout = QHBoxLayout(search_group)

        # البحث بالشهر
        month_label = QLabel("الشهر:")
        month_label.setFont(QFont("Calibri", 14, QFont.Bold))
        search_layout.addWidget(month_label)

        self.month_combo = QComboBox()
        self.month_combo.setFont(QFont("Calibri", 14, QFont.Bold))
        self.month_combo.addItems([
            "جميع الشهور", "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
            "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
        ])
        self.month_combo.currentTextChanged.connect(self.filter_table)
        search_layout.addWidget(self.month_combo)

        # اختيار السنة
        year_label = QLabel("السنة:")
        year_label.setFont(QFont("Calibri", 14, QFont.Bold))
        search_layout.addWidget(year_label)

        self.year_spin = QSpinBox()
        self.year_spin.setFont(QFont("Calibri", 14, QFont.Bold))
        self.year_spin.setRange(2020, 2030)
        self.year_spin.setValue(datetime.now().year)
        self.year_spin.valueChanged.connect(self.filter_table)
        search_layout.addWidget(self.year_spin)

        # مربع البحث
        search_label = QLabel("بحث:")
        search_label.setFont(QFont("Calibri", 14, QFont.Bold))
        search_layout.addWidget(search_label)

        self.search_box = QLineEdit()
        self.search_box.setPlaceholderText("ابحث في الشهور المرحلة...")
        self.search_box.setFont(QFont("Calibri", 14, QFont.Bold))
        self.search_box.textChanged.connect(self.filter_table)
        search_layout.addWidget(self.search_box)

        # زر تحديث
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border-radius: 6px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        refresh_btn.clicked.connect(self.load_archived_months)
        search_layout.addWidget(refresh_btn)

        main_layout.addWidget(search_group)

    def create_buttons_group(self, main_layout):
        """إنشاء مجموعة الأزرار"""
        buttons_group = QGroupBox("⚡ العمليات")
        buttons_group.setFont(QFont("Calibri", 14, QFont.Bold))
        buttons_layout = QHBoxLayout(buttons_group)

        # زر ترحيل الشهر
        archive_btn = QPushButton("🔄 ترحيل الشهر")
        archive_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        archive_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        archive_btn.clicked.connect(self.archive_month)
        buttons_layout.addWidget(archive_btn)

        # زر ترحيل السنة كاملة
        archive_year_btn = QPushButton("📅 ترحيل السنة كاملة")
        archive_year_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        archive_year_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        archive_year_btn.clicked.connect(self.archive_full_year)
        buttons_layout.addWidget(archive_year_btn)

        # زر حذف المحدد
        delete_btn = QPushButton("🗑️ حذف المحدد")
        delete_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        delete_btn.clicked.connect(self.delete_selected_month)
        buttons_layout.addWidget(delete_btn)

        # زر الإحصائيات
        stats_btn = QPushButton("📈 الإحصائيات")
        stats_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        stats_btn.setStyleSheet("""
            QPushButton {
                background-color: #6f42c1;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
        """)
        stats_btn.clicked.connect(self.show_statistics)
        buttons_layout.addWidget(stats_btn)

        # زر عرض التفاصيل
        details_btn = QPushButton("👁️ عرض التفاصيل")
        details_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        details_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        details_btn.clicked.connect(self.view_month_details)
        buttons_layout.addWidget(details_btn)

        main_layout.addWidget(buttons_group)

    def create_archived_table(self, main_layout):
        """إنشاء جدول الحسابات المرحلة"""
        table_group = QGroupBox("📊 الشهور المرحلة")
        table_group.setFont(QFont("Calibri", 14, QFont.Bold))
        table_layout = QVBoxLayout(table_group)

        self.table = QTableWidget()
        self.table.setColumnCount(8)
        self.table.setHorizontalHeaderLabels([
            "الشهر", "السنة", "عدد السجلات", "إجمالي المطلوب", "إجمالي المحصل",
            "نسبة التحصيل", "تاريخ الترحيل", "حالة البيانات"
        ])

        # تنسيق رؤوس الجدول
        header = self.table.horizontalHeader()
        header.setFont(QFont("Calibri", 13, QFont.Bold))
        header.setStyleSheet("""
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                border: 1px solid #2c3e50;
                font-weight: bold;
            }
        """)

        # تنسيق الجدول
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.table.setSortingEnabled(True)

        # تعيين عرض الأعمدة
        self.table.setColumnWidth(0, 90)   # الشهر
        self.table.setColumnWidth(1, 70)   # السنة
        self.table.setColumnWidth(2, 100)  # عدد السجلات
        self.table.setColumnWidth(3, 120)  # إجمالي المطلوب
        self.table.setColumnWidth(4, 120)  # إجمالي المحصل
        self.table.setColumnWidth(5, 100)  # نسبة التحصيل
        self.table.setColumnWidth(6, 120)  # تاريخ الترحيل
        self.table.setColumnWidth(7, 100)  # حالة البيانات

        # تنسيق الجدول
        self.table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
        """)

        # إضافة قائمة سياق للجدول
        self.table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.table.customContextMenuRequested.connect(self.show_context_menu)

        table_layout.addWidget(self.table)
        main_layout.addWidget(table_group)

        # تحميل البيانات
        self.load_archived_months()




        



    
    def get_current_month(self):
        """جلب الشهر الحالي بالعربية"""
        months = [
            "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
            "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
        ]
        return months[datetime.now().month - 1]

    def archive_month(self):
        """ترحيل شهر جديد"""
        # الحصول على الشهر والسنة من عناصر البحث
        month_index = self.month_combo.currentIndex()
        if month_index == 0:  # "جميع الشهور"
            month = self.get_current_month()
        else:
            month = self.month_combo.currentText()

        year = self.year_spin.value()

        # سؤال عن إعادة الترحيل
        reply = QMessageBox.question(
            self, "تأكيد الترحيل",
            f"هل أنت متأكد من ترحيل {month}/{year}؟\n\nملاحظة: إذا كان الشهر مرحل مسبقاً، سيتم استبدال البيانات الموجودة.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        if reply != QMessageBox.Yes:
            return

        # تنفيذ الترحيل
        self.statusBar().showMessage(f"جاري ترحيل {month}/{year}...")
        QApplication.processEvents()

        try:
            # استخدام True لإعادة الترحيل دائماً
            success, message = self.manager.archive_monthly_accounts(month, year, True)

            if success:
                QMessageBox.information(self, "نجح الترحيل", f"✅ {message}")
                self.load_archived_months()  # تحديث الجدول
                self.statusBar().showMessage(f"تم ترحيل {month}/{year} بنجاح")
            else:
                QMessageBox.warning(self, "فشل الترحيل", f"❌ {message}")
                self.statusBar().showMessage("فشل في الترحيل")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في الترحيل:\n{str(e)}")
            self.statusBar().showMessage("خطأ في الترحيل")

    def show_statistics(self):
        """عرض الإحصائيات"""
        try:
            import sqlite3
            conn = sqlite3.connect('data.db')
            cursor = conn.cursor()

            # إحصائيات عامة
            try:
                cursor.execute("SELECT COUNT(*) FROM الحسابات_المرحلة")
                total_records = cursor.fetchone()[0]
            except:
                total_records = 0

            try:
                cursor.execute("SELECT COUNT(DISTINCT month || '/' || year) FROM الحسابات_المرحلة")
                total_months = cursor.fetchone()[0]
            except:
                total_months = 0

            try:
                cursor.execute("SELECT SUM(amount_paid) FROM الحسابات_المرحلة")
                total_amount = cursor.fetchone()[0] or 0
            except:
                total_amount = 0

            conn.close()

            stats_text = f"""
📊 إحصائيات الحسابات المرحلة:

📋 إجمالي السجلات المرحلة: {total_records:,}
📅 عدد الشهور المرحلة: {total_months}
💰 إجمالي المبالغ المرحلة: {total_amount:,.2f} درهم
            """

            QMessageBox.information(self, "إحصائيات الحسابات المرحلة", stats_text)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في عرض الإحصائيات:\n{str(e)}")
    

    def load_archived_months(self):
        """تحميل الشهور المرحلة في الجدول المحسن"""
        try:
            # التأكد من وجود الجدول أولاً
            self.manager.init_database()

            # جلب البيانات المحسنة
            import sqlite3
            conn = sqlite3.connect('data.db')
            cursor = conn.cursor()

            # استعلام محسن للحصول على معلومات أكثر
            cursor.execute("""
                SELECT
                    month,
                    year,
                    COUNT(*) as record_count,
                    SUM(amount_required) as total_required,
                    SUM(amount_paid) as total_paid,
                    MIN(ترحيل_date) as first_archive,
                    MAX(ترحيل_date) as last_archive
                FROM الحسابات_المرحلة
                GROUP BY month, year
                ORDER BY year DESC,
                    CASE month
                        WHEN 'يناير' THEN 1
                        WHEN 'فبراير' THEN 2
                        WHEN 'مارس' THEN 3
                        WHEN 'أبريل' THEN 4
                        WHEN 'مايو' THEN 5
                        WHEN 'يونيو' THEN 6
                        WHEN 'يوليو' THEN 7
                        WHEN 'أغسطس' THEN 8
                        WHEN 'سبتمبر' THEN 9
                        WHEN 'أكتوبر' THEN 10
                        WHEN 'نوفمبر' THEN 11
                        WHEN 'ديسمبر' THEN 12
                        ELSE 13
                    END DESC
            """)

            archived_months = cursor.fetchall()
            conn.close()

            self.table.setRowCount(len(archived_months))

            for row, month_data in enumerate(archived_months):
                month, year, record_count, total_required, total_paid, first_archive, last_archive = month_data

                # حساب نسبة التحصيل
                collection_rate = (total_paid / total_required * 100) if total_required and total_required > 0 else 0

                # الشهر
                month_item = QTableWidgetItem(month)
                month_item.setTextAlignment(Qt.AlignCenter)
                month_item.setFont(QFont("Calibri", 13, QFont.Bold))
                self.table.setItem(row, 0, month_item)

                # السنة
                year_item = QTableWidgetItem(str(year))
                year_item.setTextAlignment(Qt.AlignCenter)
                year_item.setFont(QFont("Calibri", 13, QFont.Bold))
                self.table.setItem(row, 1, year_item)

                # عدد السجلات
                count_item = QTableWidgetItem(f"{record_count:,}")
                count_item.setTextAlignment(Qt.AlignCenter)
                count_item.setFont(QFont("Calibri", 13, QFont.Bold))
                # تلوين حسب عدد السجلات
                if record_count > 100:
                    count_item.setBackground(QColor(213, 244, 230))  # أخضر فاتح
                elif record_count > 50:
                    count_item.setBackground(QColor(255, 243, 205))  # أصفر فاتح
                else:
                    count_item.setBackground(QColor(248, 215, 218))  # أحمر فاتح
                self.table.setItem(row, 2, count_item)

                # إجمالي المطلوب
                required_item = QTableWidgetItem(f"{total_required:,.2f}" if total_required else "0.00")
                required_item.setTextAlignment(Qt.AlignCenter)
                required_item.setFont(QFont("Calibri", 13, QFont.Bold))
                self.table.setItem(row, 3, required_item)

                # إجمالي المحصل
                paid_item = QTableWidgetItem(f"{total_paid:,.2f}" if total_paid else "0.00")
                paid_item.setTextAlignment(Qt.AlignCenter)
                paid_item.setFont(QFont("Calibri", 13, QFont.Bold))
                self.table.setItem(row, 4, paid_item)

                # نسبة التحصيل
                rate_item = QTableWidgetItem(f"{collection_rate:.1f}%")
                rate_item.setTextAlignment(Qt.AlignCenter)
                rate_item.setFont(QFont("Calibri", 13, QFont.Bold))
                # تلوين حسب نسبة التحصيل
                if collection_rate >= 90:
                    rate_item.setBackground(QColor(213, 244, 230))  # أخضر فاتح
                elif collection_rate >= 70:
                    rate_item.setBackground(QColor(255, 243, 205))  # أصفر فاتح
                else:
                    rate_item.setBackground(QColor(248, 215, 218))  # أحمر فاتح
                self.table.setItem(row, 5, rate_item)

                # تاريخ الترحيل
                first_date = first_archive.split()[0] if first_archive else "غير محدد"
                first_item = QTableWidgetItem(first_date)
                first_item.setTextAlignment(Qt.AlignCenter)
                first_item.setFont(QFont("Calibri", 13, QFont.Bold))
                self.table.setItem(row, 6, first_item)

                # حالة البيانات
                status = "مكتمل" if record_count > 0 else "فارغ"
                status_item = QTableWidgetItem(status)
                status_item.setTextAlignment(Qt.AlignCenter)
                status_item.setFont(QFont("Calibri", 13, QFont.Bold))
                if status == "مكتمل":
                    status_item.setBackground(QColor(213, 244, 230))  # أخضر فاتح
                else:
                    status_item.setBackground(QColor(248, 215, 218))  # أحمر فاتح
                self.table.setItem(row, 7, status_item)

            self.statusBar().showMessage(f"تم تحميل {len(archived_months)} شهر مرحل")

        except Exception as e:
            print(f"خطأ في تحميل البيانات المرحلة: {str(e)}")
            # في حالة عدم وجود الجدول، عرض جدول فارغ
            self.table.setRowCount(0)
            self.statusBar().showMessage("لا توجد بيانات مرحلة أو لم يتم إنشاء الجدول بعد")

    def filter_table(self):
        """تصفية الجدول حسب المعايير المحددة"""
        search_text = self.search_box.text().lower()
        selected_month = self.month_combo.currentText()
        selected_year = self.year_spin.value()

        for row in range(self.table.rowCount()):
            show_row = True

            # تصفية بالنص
            if search_text:
                row_text = ""
                for col in range(self.table.columnCount()):
                    item = self.table.item(row, col)
                    if item:
                        row_text += item.text().lower() + " "
                if search_text not in row_text:
                    show_row = False

            # تصفية بالشهر
            if selected_month != "جميع الشهور" and show_row:
                month_item = self.table.item(row, 0)
                if month_item and month_item.text() != selected_month:
                    show_row = False

            # تصفية بالسنة (تجاهل التصفية بالسنة إذا كانت القيمة الافتراضية)
            # if show_row:
            #     year_item = self.table.item(row, 1)
            #     if year_item and int(year_item.text()) != selected_year:
            #         show_row = False

            self.table.setRowHidden(row, not show_row)
    
    def delete_selected_month(self):
        """حذف الشهر المحدد"""
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار شهر للحذف")
            return
        
        month = self.table.item(current_row, 0).text()
        year = int(self.table.item(current_row, 1).text())
        record_count = self.table.item(current_row, 2).text()
        
        # تأكيد الحذف
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف ترحيل {month}/{year}؟\n"
            f"سيتم حذف {record_count} سجل نهائياً!",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                success, message = self.manager.delete_archived_month(month, year)
                
                if success:
                    QMessageBox.information(self, "تم الحذف", f"✅ {message}")
                    self.load_archived_months()  # تحديث الجدول
                    self.statusBar().showMessage(f"تم حذف {month}/{year}")
                else:
                    QMessageBox.warning(self, "فشل الحذف", f"❌ {message}")
                    
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ في الحذف:\n{str(e)}")



    def archive_full_year(self):
        """ترحيل السنة كاملة"""
        year = self.year_spin.value()
        # استخدام True لإعادة الترحيل دائماً

        # تأكيد العملية
        reply = QMessageBox.question(
            self, "تأكيد ترحيل السنة",
            f"هل أنت متأكد من ترحيل جميع شهور السنة {year}؟\n"
            f"هذه العملية قد تستغرق وقتاً طويلاً.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        # تنفيذ الترحيل
        months = [
            "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
            "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
        ]

        success_count = 0
        total_months = len(months)

        # شريط التقدم
        progress = QProgressDialog(f"جاري ترحيل السنة {year}...", "إلغاء", 0, total_months, self)
        progress.setWindowModality(Qt.WindowModal)
        progress.show()

        for i, month in enumerate(months):
            if progress.wasCanceled():
                break

            progress.setLabelText(f"جاري ترحيل {month} {year}...")
            progress.setValue(i)
            QApplication.processEvents()

            try:
                success, message = self.manager.archive_monthly_accounts(month, year, True)
                if success:
                    success_count += 1
            except Exception as e:
                print(f"خطأ في ترحيل {month}: {e}")

        progress.setValue(total_months)
        progress.close()

        # عرض النتيجة
        QMessageBox.information(
            self, "انتهى ترحيل السنة",
            f"تم ترحيل {success_count} من {total_months} شهر بنجاح للسنة {year}"
        )

        self.load_archived_months()



    def show_context_menu(self, position):
        """عرض قائمة السياق للجدول"""
        if self.table.itemAt(position) is None:
            return

        menu = QMenu(self)

        # إجراءات القائمة
        view_details_action = menu.addAction("👁️ عرض التفاصيل")
        menu.addSeparator()
        re_archive_action = menu.addAction("🔄 إعادة ترحيل")
        export_month_action = menu.addAction("📤 تصدير هذا الشهر")
        menu.addSeparator()
        delete_action = menu.addAction("🗑️ حذف")

        # تنفيذ الإجراء المحدد
        action = menu.exec_(self.table.mapToGlobal(position))

        if action == view_details_action:
            self.view_month_details()
        elif action == re_archive_action:
            self.re_archive_selected_month()
        elif action == export_month_action:
            self.export_selected_month()
        elif action == delete_action:
            self.delete_selected_month()

    def view_month_details(self):
        """عرض تفاصيل الشهر المحدد"""
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تنبيه", "يرجى اختيار شهر من الجدول أولاً")
            return

        month = self.table.item(current_row, 0).text()
        year = int(self.table.item(current_row, 1).text())

        # نافذة تفاصيل الشهر
        details_dialog = QDialog(self)
        details_dialog.setWindowTitle(f"تفاصيل {month}/{year}")
        details_dialog.setGeometry(200, 200, 1000, 700)
        details_dialog.setLayoutDirection(Qt.RightToLeft)

        # توسيط النافذة
        self.center_window(details_dialog)

        layout = QVBoxLayout(details_dialog)

        # معلومات الشهر
        info_label = QLabel(f"📊 تفاصيل الحسابات المرحلة لشهر {month}/{year}")
        info_label.setFont(QFont("Calibri", 16, QFont.Bold))
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #2c3e50;
                color: white;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(info_label)

        try:
            # جلب البيانات من قاعدة البيانات
            import sqlite3
            conn = sqlite3.connect('data.db')
            cursor = conn.cursor()

            # جلب تفاصيل الحسابات المرحلة لهذا الشهر
            cursor.execute("""
                SELECT
                    COALESCE(jb.اسم_التلميذ, 'غير محدد') as اسم_التلميذ,
                    COALESCE(jb.رمز_التلميذ, 'غير محدد') as رمز_التلميذ,
                    COALESCE(hm.amount_required, 0) as amount_required,
                    COALESCE(hm.amount_paid, 0) as amount_paid,
                    COALESCE(hm.payment_status, 'غير محدد') as payment_status,
                    COALESCE(hm.ترحيل_date, 'غير محدد') as ترحيل_date,
                    COALESCE('', '') as notes
                FROM الحسابات_المرحلة hm
                LEFT JOIN جدول_البيانات jb ON hm.student_id = jb.id
                WHERE hm.month = ? AND hm.year = ?
                ORDER BY jb.اسم_التلميذ
            """, (month, year))

            records = cursor.fetchall()
            conn.close()

            if not records:
                # إذا لم توجد بيانات
                no_data_label = QLabel("❌ لا توجد بيانات مرحلة لهذا الشهر")
                no_data_label.setFont(QFont("Calibri", 14, QFont.Bold))
                no_data_label.setAlignment(Qt.AlignCenter)
                no_data_label.setStyleSheet("color: #dc3545; padding: 20px;")
                layout.addWidget(no_data_label)
            else:
                # إحصائيات سريعة
                total_required = sum(record[2] for record in records if record[2])
                total_paid = sum(record[3] for record in records if record[3])
                collection_rate = (total_paid / total_required * 100) if total_required > 0 else 0

                stats_label = QLabel(f"""
📈 الإحصائيات:
👥 عدد التلاميذ: {len(records)}
💰 إجمالي المطلوب: {total_required:,.2f} درهم
💵 إجمالي المحصل: {total_paid:,.2f} درهم
📊 نسبة التحصيل: {collection_rate:.1f}%
                """)
                stats_label.setFont(QFont("Calibri", 13, QFont.Bold))
                stats_label.setStyleSheet("""
                    QLabel {
                        background-color: #f8f9fa;
                        border: 1px solid #dee2e6;
                        border-radius: 5px;
                        padding: 10px;
                        margin: 5px;
                    }
                """)
                layout.addWidget(stats_label)

                # جدول التفاصيل
                details_table = QTableWidget()
                details_table.setColumnCount(7)
                details_table.setHorizontalHeaderLabels([
                    "اسم التلميذ", "رمز التلميذ", "المبلغ المطلوب", "المبلغ المدفوع",
                    "حالة الدفع", "تاريخ الترحيل", "ملاحظات"
                ])

                # تنسيق رؤوس الجدول
                header = details_table.horizontalHeader()
                header.setFont(QFont("Calibri", 13, QFont.Bold))
                header.setStyleSheet("""
                    QHeaderView::section {
                        background-color: #34495e;
                        color: white;
                        padding: 8px;
                        border: 1px solid #2c3e50;
                        font-weight: bold;
                    }
                """)

                # ملء البيانات
                details_table.setRowCount(len(records))
                for row, record in enumerate(records):
                    اسم_التلميذ, رمز_التلميذ, amount_required, amount_paid, payment_status, ترحيل_date, notes = record

                    # اسم التلميذ
                    name_item = QTableWidgetItem(اسم_التلميذ or "")
                    name_item.setFont(QFont("Calibri", 13, QFont.Bold))
                    details_table.setItem(row, 0, name_item)

                    # رمز التلميذ
                    code_item = QTableWidgetItem(رمز_التلميذ or "")
                    code_item.setFont(QFont("Calibri", 13, QFont.Bold))
                    code_item.setTextAlignment(Qt.AlignCenter)
                    details_table.setItem(row, 1, code_item)

                    # المبلغ المطلوب
                    required_item = QTableWidgetItem(f"{amount_required:.2f}" if amount_required else "0.00")
                    required_item.setFont(QFont("Calibri", 13, QFont.Bold))
                    required_item.setTextAlignment(Qt.AlignCenter)
                    details_table.setItem(row, 2, required_item)

                    # المبلغ المدفوع
                    paid_item = QTableWidgetItem(f"{amount_paid:.2f}" if amount_paid else "0.00")
                    paid_item.setFont(QFont("Calibri", 13, QFont.Bold))
                    paid_item.setTextAlignment(Qt.AlignCenter)
                    details_table.setItem(row, 3, paid_item)

                    # حالة الدفع
                    status_item = QTableWidgetItem(payment_status or "غير محدد")
                    status_item.setFont(QFont("Calibri", 13, QFont.Bold))
                    status_item.setTextAlignment(Qt.AlignCenter)
                    # تلوين حسب حالة الدفع
                    if payment_status == "مدفوع":
                        status_item.setBackground(QColor(213, 244, 230))  # أخضر فاتح
                    elif payment_status == "جزئي":
                        status_item.setBackground(QColor(255, 243, 205))  # أصفر فاتح
                    else:
                        status_item.setBackground(QColor(248, 215, 218))  # أحمر فاتح
                    details_table.setItem(row, 4, status_item)

                    # تاريخ الترحيل
                    date_item = QTableWidgetItem(ترحيل_date or "")
                    date_item.setFont(QFont("Calibri", 13, QFont.Bold))
                    date_item.setTextAlignment(Qt.AlignCenter)
                    details_table.setItem(row, 5, date_item)

                    # ملاحظات
                    notes_item = QTableWidgetItem(notes or "")
                    notes_item.setFont(QFont("Calibri", 13, QFont.Bold))
                    details_table.setItem(row, 6, notes_item)

                # تنسيق الجدول
                details_table.setAlternatingRowColors(True)
                details_table.setSelectionBehavior(QAbstractItemView.SelectRows)
                details_table.setSortingEnabled(True)
                details_table.resizeColumnsToContents()

                layout.addWidget(details_table)

        except Exception as e:
            error_label = QLabel(f"❌ خطأ في تحميل البيانات: {str(e)}")
            error_label.setFont(QFont("Calibri", 14, QFont.Bold))
            error_label.setStyleSheet("color: #dc3545; padding: 20px;")
            layout.addWidget(error_label)

        # زر إغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.setFont(QFont("Calibri", 13, QFont.Bold))
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        close_btn.clicked.connect(details_dialog.close)
        layout.addWidget(close_btn)

        details_dialog.exec_()

    def re_archive_selected_month(self):
        """إعادة ترحيل الشهر المحدد"""
        current_row = self.table.currentRow()
        if current_row < 0:
            return

        month = self.table.item(current_row, 0).text()
        year = int(self.table.item(current_row, 1).text())

        # تأكيد إعادة الترحيل
        reply = QMessageBox.question(
            self, "تأكيد إعادة الترحيل",
            f"هل أنت متأكد من إعادة ترحيل {month}/{year}؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                success, message = self.manager.archive_monthly_accounts(month, year, force_update=True)

                if success:
                    QMessageBox.information(self, "نجح إعادة الترحيل", f"✅ {message}")
                    self.load_archived_months()
                else:
                    QMessageBox.warning(self, "فشل إعادة الترحيل", f"❌ {message}")

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ في إعادة الترحيل:\n{str(e)}")

    def export_selected_month(self):
        """تصدير الشهر المحدد"""
        QMessageBox.information(self, "قريباً", "ميزة التصدير ستكون متاحة قريباً")



if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle('Fusion')

    window = ArchivedAccountsWindow()
    window.show()

    sys.exit(app.exec_())
