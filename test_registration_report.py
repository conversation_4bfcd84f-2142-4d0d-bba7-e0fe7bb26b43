#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""اختبار تقرير واجبات التسجيل"""

import os
import sys

def test_registration_report():
    """اختبار إنشاء تقرير واجبات التسجيل"""
    try:
        print("🔍 اختبار تقرير واجبات التسجيل...")
        
        # استيراد الوحدة
        try:
            from print_registration_fees_monthly_style import print_registration_fees_report
            print("✅ تم استيراد الوحدة بنجاح")
        except ImportError as e:
            print(f"❌ فشل في استيراد الوحدة: {e}")
            return
        
        # اختبار إنشاء التقرير
        print("\n🔧 إنشاء تقرير تجريبي...")
        success, output_path, message = print_registration_fees_report(
            section="قسم / 02"
        )
        
        print(f"\n📊 النتائج:")
        print(f"   - النجاح: {success}")
        print(f"   - المسار: {output_path}")
        print(f"   - الرسالة: {message}")
        
        # تحقق من وجود الملف
        if output_path and os.path.exists(output_path):
            print(f"✅ الملف موجود: {output_path}")
            print(f"📁 حجم الملف: {os.path.getsize(output_path)} بايت")
        else:
            print(f"❌ الملف غير موجود: {output_path}")
        
        # تحقق من وجود المجلد
        reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير واجبات التسجيل')
        print(f"\n📁 مجلد التقارير: {reports_dir}")
        
        if os.path.exists(reports_dir):
            print("✅ المجلد موجود")
            files = os.listdir(reports_dir)
            print(f"📄 عدد الملفات في المجلد: {len(files)}")
            for file in files:
                print(f"   - {file}")
        else:
            print("❌ المجلد غير موجود")
        
        print("\n✅ انتهى الاختبار")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_registration_report()
